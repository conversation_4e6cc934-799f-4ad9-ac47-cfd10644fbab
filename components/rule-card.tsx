"use client";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { 
  MoreHorizontal, 
  Copy, 
  Share, 
  Download, 
  Edit, 
  Trash,
  Eye,
  Code,
  FileJson,
  FileText,
  Terminal
} from "lucide-react";
import { Rule } from "@/lib/store";
import { CodeEditor } from "@/components/ui/code-editor";
import { toast } from "sonner";

interface RuleCardProps {
  rule: Rule;
  onEdit?: (rule: Rule) => void;
  onDelete?: (ruleId: string) => void;
  isOwner?: boolean;
}

export function RuleCard({ rule, onEdit, onDelete, isOwner = false }: RuleCardProps) {
  const [showContent, setShowContent] = useState(false);
  const [copiedCommand, setCopiedCommand] = useState(false);
  
  const handleCopy = async () => {
    await navigator.clipboard.writeText(rule.content);
    toast.success("Rule content copied to clipboard");
  };

  const handleCopyCommand = async () => {
    // Only available for public rules
    if (rule.visibility !== "PUBLIC") {
      toast.error("Only public rules can be used with npx command");
      return;
    }

    const rawDataUrl = `${window.location.origin}/api/rules/raw?id=${rule.id}`;
    const command = `npx onlyrules -f "${rawDataUrl}"`;
    
    await navigator.clipboard.writeText(command);
    setCopiedCommand(true);
    toast.success("CLI command copied to clipboard");
    
    // Reset the copied state after 2 seconds
    setTimeout(() => setCopiedCommand(false), 2000);
  };

  const handleShare = async () => {
    if (rule.shareToken) {
      const shareUrl = `${window.location.origin}/shared/${rule.shareToken}`;
      await navigator.clipboard.writeText(shareUrl);
      toast.success("Share link copied to clipboard");
    }
  };

  const handleDownloadJSON = () => {
    const data = {
      title: rule.title,
      description: rule.description,
      content: rule.content,
      ideType: rule.ideType,
      tags: rule.tags.map(t => t.tag.name),
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: "application/json",
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${rule.title.toLowerCase().replace(/\s+/g, "-")}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success("Rule downloaded as JSON");
  };

  const handleDownloadMDX = async () => {
    // Only download MDX for public rules via API
    if (rule.visibility !== "PUBLIC") {
      toast.error("Only public rules can be downloaded as MDX");
      return;
    }

    try {
      const response = await fetch(`/api/rules/download?id=${rule.id}`);
      if (!response.ok) {
        throw new Error("Failed to download rule");
      }
      
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `${rule.title.toLowerCase().replace(/\s+/g, "-")}.mdx`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast.success("Rule downloaded as MDX");
    } catch (error) {
      toast.error("Failed to download rule as MDX");
    }
  };

  const getIDEBadgeColor = (ideType: string) => {
    switch (ideType) {
      case "CURSOR":
        return "bg-blue-500";
      case "AUGMENT":
        return "bg-green-500";
      case "WINDSURF":
        return "bg-purple-500";
      case "CLAUDE":
        return "bg-orange-500";
      case "GITHUB_COPILOT":
        return "bg-gray-800";
      case "GEMINI":
        return "bg-indigo-500";
      case "OPENAI_CODEX":
        return "bg-teal-500";
      case "CLINE":
        return "bg-pink-500";
      case "JUNIE":
        return "bg-yellow-500";
      case "TRAE":
        return "bg-red-500";
      case "LINGMA":
        return "bg-cyan-500";
      case "KIRO":
        return "bg-emerald-500";
      case "TENCENT_CODEBUDDY":
        return "bg-violet-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <>
      <Card className="group hover:shadow-md transition-all duration-200">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <CardTitle 
                className="text-lg cursor-pointer hover:text-primary transition-colors"
                onClick={() => setShowContent(true)}
              >
                {rule.title}
              </CardTitle>
              {rule.description && (
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {rule.description}
                </p>
              )}
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger>
                <Button
                  variant="ghost"
                  size="1"
                  className="opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setShowContent(true)}>
                  <Eye className="mr-2 h-4 w-4" />
                  View
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleCopy}>
                  <Copy className="mr-2 h-4 w-4" />
                  Copy Content
                </DropdownMenuItem>
                  <>
                    <TooltipProvider>
                      <Tooltip content={
                        <div className="max-w-xs">
                          <p className="text-xs">
                            Copy command to use this rule with onlyrules CLI:
                          </p>
                          <code className="text-xs bg-muted px-1 py-0.5 rounded">
                            npx onlyrules -f "[url]"
                          </code>
                        </div>
                      }>
                        <DropdownMenuItem onClick={handleCopyCommand}>
                          <Terminal className="mr-2 h-4 w-4" />
                          Copy CLI Command
                        </DropdownMenuItem>
                      </Tooltip>
                    </TooltipProvider>
                    <DropdownMenuItem onClick={handleShare}>
                      <Share className="mr-2 h-4 w-4" />
                      Share
                    </DropdownMenuItem>
                  </>
                <DropdownMenuSub>
                  <DropdownMenuSubTrigger>
                    <Download className="mr-2 h-4 w-4" />
                    Download
                  </DropdownMenuSubTrigger>
                  <DropdownMenuSubContent>
                    <DropdownMenuItem onClick={handleDownloadJSON}>
                      <FileJson className="mr-2 h-4 w-4" />
                      JSON
                    </DropdownMenuItem>
                      <DropdownMenuItem onClick={handleDownloadMDX}>
                        <FileText className="mr-2 h-4 w-4" />
                        MDX
                      </DropdownMenuItem>
                  </DropdownMenuSubContent>
                </DropdownMenuSub>
                {isOwner && onEdit && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => onEdit(rule)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                  </>
                )}
                {isOwner && onDelete && (
                  <DropdownMenuItem 
                    onClick={() => onDelete(rule.id)}
                    className="text-destructive"
                  >
                    <Trash className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="flex items-center gap-2">
            <Badge
              variant="soft"
              className={`${getIDEBadgeColor(rule.ideType)} text-white`}
            >
              <Code className="mr-1 h-3 w-3" />
              {rule.ideType}
            </Badge>
            {rule.visibility === "PUBLIC" && (
              <Badge variant="outline">Public</Badge>
            )}
          </div>
          
          {rule.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {rule.tags.map((ruleTag) => (
                <Badge
                  key={ruleTag.tag.id}
                  variant="outline"
                  style={{ borderColor: ruleTag.tag.color }}
                  className="text-xs"
                >
                  {ruleTag.tag.name}
                </Badge>
              ))}
            </div>
          )}
          
          <div className="text-xs text-muted-foreground">
            Updated {new Date(rule.updatedAt).toLocaleDateString()}
          </div>
        </CardContent>
      </Card>

      <Dialog open={showContent} onOpenChange={setShowContent}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>{rule.title}</DialogTitle>
            <DialogDescription>
              {rule.description}
            </DialogDescription>
          </DialogHeader>
          <div className="flex-1 overflow-hidden">
            <CodeEditor 
              value={rule.content} 
              onChange={() => {}} 
              className="h-[60vh]"
            />
          </div>
          {rule.visibility === "PUBLIC" && (
            <div className="mt-4 p-3 bg-muted rounded-lg">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Use with onlyrules CLI:</p>
                  <code className="text-xs bg-background px-2 py-1 rounded">
                    npx onlyrules generate -f "{window.location.origin}/api/rules/raw?id={rule.id}"
                  </code>
                </div>
                <Button
                  size="1"
                  variant="outline"
                  onClick={handleCopyCommand}
                  className="ml-2"
                >
                  <Terminal className="h-4 w-4 mr-2" />
                  {copiedCommand ? "Copied!" : "Copy"}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}