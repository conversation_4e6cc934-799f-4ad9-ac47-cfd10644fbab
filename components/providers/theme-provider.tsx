"use client";

import * as React from "react";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { type ThemeProviderProps } from "next-themes";
import { Theme } from "@radix-ui/themes";

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return (
    <NextThemesProvider {...props}>
      <Theme accentColor="blue" grayColor="gray" radius="medium" scaling="100%">
        {children}
      </Theme>
    </NextThemesProvider>
  );
}