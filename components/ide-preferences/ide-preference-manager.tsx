"use client";

import { useState } from "react";
import { use<PERSON><PERSON> } from "jotai";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Plus, 
  Trash2, 
  Star, 
  StarOff,
  Code,
  Settings
} from "lucide-react";
import { 
  idePreferencesAtom, 
  IDEPreference, 
  IDEType, 
  IDE_METADATA 
} from "@/lib/store";
import { toast } from "sonner";

interface IDEPreferenceManagerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function IDEPreferenceManager({ open, onOpenChange }: IDEPreferenceManagerProps) {
  const [preferences, setPreferences] = useAtom(idePreferencesAtom);
  const [selectedIDE, setSelectedIDE] = useState<IDEType | "">("");

  const availableIDEs = Object.entries(IDE_METADATA).filter(
    ([type]) => !preferences.preferredIDEs.some(pref => pref.type === type)
  );

  const handleAddIDE = () => {
    if (!selectedIDE) return;

    const newIDE: IDEPreference = {
      id: `${selectedIDE}-${Date.now()}`,
      name: IDE_METADATA[selectedIDE].name,
      type: selectedIDE,
      isDefault: preferences.preferredIDEs.length === 0, // First IDE becomes default
      addedAt: new Date().toISOString(),
    };

    const updatedPreferences = {
      ...preferences,
      preferredIDEs: [...preferences.preferredIDEs, newIDE],
      defaultIDE: preferences.preferredIDEs.length === 0 ? newIDE.id : preferences.defaultIDE,
    };

    setPreferences(updatedPreferences);
    setSelectedIDE("");
    toast.success(`${IDE_METADATA[selectedIDE].name} added to preferences`);
  };

  const handleRemoveIDE = (ideId: string) => {
    const ideToRemove = preferences.preferredIDEs.find(ide => ide.id === ideId);
    const updatedIDEs = preferences.preferredIDEs.filter(ide => ide.id !== ideId);
    
    let newDefaultIDE = preferences.defaultIDE;
    if (preferences.defaultIDE === ideId) {
      // If removing the default IDE, set the first remaining IDE as default
      newDefaultIDE = updatedIDEs.length > 0 ? updatedIDEs[0].id : undefined;
    }

    setPreferences({
      preferredIDEs: updatedIDEs,
      defaultIDE: newDefaultIDE,
    });

    if (ideToRemove) {
      toast.success(`${ideToRemove.name} removed from preferences`);
    }
  };

  const handleSetDefault = (ideId: string) => {
    const ide = preferences.preferredIDEs.find(ide => ide.id === ideId);
    setPreferences({
      ...preferences,
      defaultIDE: ideId,
    });
    
    if (ide) {
      toast.success(`${ide.name} set as default IDE`);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            IDE Preferences
          </DialogTitle>
          <DialogDescription>
            Manage your preferred IDEs for quick command generation. Set a default IDE and add your favorites.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Add New IDE Section */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium">Add IDE</h3>
            <div className="flex gap-2">
              <Select value={selectedIDE} onValueChange={setSelectedIDE}>
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder="Select an IDE to add..." />
                </SelectTrigger>
                <SelectContent>
                  {availableIDEs.map(([type, metadata]) => (
                    <SelectItem key={type} value={type}>
                      <div className="flex items-center gap-2">
                        <Code className="h-4 w-4" />
                        <div>
                          <div className="font-medium">{metadata.name}</div>
                          <div className="text-xs text-muted-foreground">{metadata.description}</div>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button 
                onClick={handleAddIDE} 
                disabled={!selectedIDE}
                size="sm"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add
              </Button>
            </div>
          </div>

          {/* Preferred IDEs List */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium">
              Preferred IDEs ({preferences.preferredIDEs.length})
            </h3>
            
            {preferences.preferredIDEs.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Code className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No preferred IDEs added yet</p>
                <p className="text-xs">Add your favorite IDEs to generate quick commands</p>
              </div>
            ) : (
              <div className="space-y-2">
                {preferences.preferredIDEs.map((ide) => (
                  <div
                    key={ide.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <Code className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{ide.name}</span>
                          {preferences.defaultIDE === ide.id && (
                            <Badge variant="soft" className="text-xs">
                              Default
                            </Badge>
                          )}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {IDE_METADATA[ide.type].description}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSetDefault(ide.id)}
                        disabled={preferences.defaultIDE === ide.id}
                        className="h-8 w-8 p-0"
                      >
                        {preferences.defaultIDE === ide.id ? (
                          <Star className="h-4 w-4 fill-current" />
                        ) : (
                          <StarOff className="h-4 w-4" />
                        )}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveIDE(ide.id)}
                        className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Help Text */}
          <div className="text-xs text-muted-foreground bg-muted p-3 rounded-lg">
            <p className="font-medium mb-1">How it works:</p>
            <ul className="space-y-1">
              <li>• Add your favorite IDEs to generate quick npx commands</li>
              <li>• Set a default IDE for one-click command copying</li>
              <li>• Commands will include the --target flag for your selected IDE</li>
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
