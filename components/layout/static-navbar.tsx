import Link from "next/link";

export function StaticNavbar() {
  return (
    <nav className="sticky top-0 z-50 w-full border-b bg-white">
      <div className="container mx-auto flex h-16 items-center justify-between px-4">
        <div className="flex items-center gap-8">
          <Link href="/" className="flex items-center gap-2">
            <span className="font-bold text-xl">OnlyRules</span>
          </Link>

          <div className="hidden md:flex items-center gap-6">
            <Link
              href="/templates"
              className="text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors"
            >
              Templates
            </Link>
            <Link
              href="/tutorials"
              className="text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors"
            >
              Tutorials
            </Link>
            <Link
              href="/dashboard"
              className="text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors"
            >
              Dashboard
            </Link>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <Link
            href="https://github.com/ranglang/onlyrules"
            target="_blank"
            rel="noopener noreferrer"
            className="text-sm text-gray-600 hover:text-gray-900"
          >
            GitHub
          </Link>

          <Link
            href="/auth/signin"
            className="bg-blue-500 text-white px-4 py-2 rounded text-sm hover:bg-blue-600"
          >
            Sign In
          </Link>
        </div>
      </div>
    </nav>
  );
}
