import Link from "next/link";
import { Code, Github } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

export function StaticNavbar() {
  return (
    <nav className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-8">
          <Link href="/" className="flex items-center gap-2">
            <Code className="h-6 w-6 text-primary" />
            <span className="font-bold text-xl">OnlyRules</span>
          </Link>
          
          <div className="hidden md:flex items-center gap-6">
            <Link 
              href="/templates" 
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Templates
            </Link>
            <Link 
              href="/tutorials" 
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Tutorials
            </Link>
            <Link 
              href="/dashboard" 
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Dashboard
            </Link>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <Button variant="ghost" size="2" asChild>
            <Link
              href="https://github.com/ranglang/onlyrules"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2"
            >
              <Github className="h-4 w-4" />
              <span className="hidden sm:inline">GitHub</span>
            </Link>
          </Button>

          <Button size="2" asChild>
            <Link href="/auth/signin">
              Sign In
            </Link>
          </Button>
        </div>
      </div>
    </nav>
  );
}
