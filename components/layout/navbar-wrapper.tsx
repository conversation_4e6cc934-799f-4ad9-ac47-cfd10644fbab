import { ClientNavbar } from './client-navbar'
import { getLocale } from '@/lib/locale'
import { Suspense } from 'react'
import { StaticNavbar } from './static-navbar'
import { defaultLocale } from '@/lib/i18n'

// Fallback component for SSG
function NavbarFallback() {
  return <StaticNavbar />;
}

export function NavbarWrapper() {
  // Use default locale during static generation
  let locale;
  try {
    locale = getLocale();
  } catch (error) {
    // During static generation, use default locale
    locale = defaultLocale;
  }

  return (
    <Suspense fallback={<NavbarFallback />}>
      <ClientNavbar locale={locale} />
    </Suspense>
  );
}