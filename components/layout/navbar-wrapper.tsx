import { ClientNavbar } from './client-navbar'
import { getLocale } from '@/lib/locale'
import { Suspense } from 'react'
import { StaticNavbar } from './static-navbar'

// Fallback component for SSG
function NavbarFallback() {
  return <StaticNavbar />;
}

export function NavbarWrapper() {
  const locale = getLocale();
  
  return (
    <Suspense fallback={<NavbarFallback />}>
      <ClientNavbar locale={locale} />
    </Suspense>
  );
}