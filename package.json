{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "bun run next dev", "build": "bun run next build", "start": "bun run next start", "lint": "bun run next lint", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "postinstall": "bun run prisma generate", "install:clean": "rm -rf node_modules bun.lockb && bun install", "type-check": "bun run tsc --noEmit", "db:generate": "bun run prisma generate", "db:push": "bun run prisma db push", "db:studio": "bun run prisma studio", "db:migrate": "bun run prisma migrate dev", "db:migrate:deploy": "bun run prisma migrate deploy", "db:migrate:reset": "bun run prisma migrate reset", "db:seed": "bun run prisma db seed", "lingui:extract": "lingui extract", "lingui:compile": "lingui compile"}, "dependencies": {"@codemirror/basic-setup": "^0.20.0", "@codemirror/lang-javascript": "^6.2.4", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.3", "@codemirror/view": "^6.38.0", "@hookform/resolvers": "^3.9.0", "@lingui/cli": "^5.3.3", "@lingui/core": "^5.3.3", "@lingui/format-json": "^5.3.3", "@lingui/loader": "^5.3.3", "@lingui/macro": "^5.3.3", "@lingui/react": "^5.3.3", "@next/swc-wasm-nodejs": "13.5.1", "@next/third-parties": "^15.3.5", "@prisma/client": "^6.11.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/themes": "^3.2.1", "@types/bcryptjs": "^3.0.0", "@types/node": "20.6.2", "@types/react": "18.2.22", "@types/react-dom": "18.2.7", "@uiw/react-codemirror": "^4.24.0", "autoprefixer": "10.4.15", "babel-plugin-macros": "^3.1.0", "bcryptjs": "^3.0.2", "better-auth": "^1.2.12", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "codemirror": "^6.0.2", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "eslint": "8.49.0", "eslint-config-next": "13.5.1", "input-otp": "^1.2.4", "jose": "^6.0.11", "jotai": "^2.12.5", "lucide-react": "^0.446.0", "next": "13.5.1", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "onlyrules": "^0.0.24", "postcss": "8.4.30", "prisma": "^6.11.1", "react": "18.2.0", "react-day-picker": "^8.10.1", "react-dom": "18.2.0", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "typescript": "5.2.2", "vaul": "^0.9.9", "zod": "^3.23.8"}}