import Link from "next/link";
import { StaticNavbar } from "@/components/layout/static-navbar";

// Force static generation for this page
export const dynamic = 'force-static';

export default function HomePage() {

  return (
    <div className="flex flex-col min-h-screen">
      <StaticNavbar />
      <div className="container py-20">
        <h1 className="text-4xl font-bold text-center">OnlyRules</h1>
        <p className="text-xl text-center mt-4">AI Prompt Management Platform</p>
        <div className="flex justify-center gap-4 mt-8">
          <Link href="/auth/signin" className="bg-blue-500 text-white px-6 py-2 rounded">
            Get Started
          </Link>
          <Link href="/templates" className="border border-gray-300 px-6 py-2 rounded">
            Browse Templates
          </Link>
        </div>
      </div>
    </div>
  );
}
