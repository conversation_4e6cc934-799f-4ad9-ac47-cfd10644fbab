import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { 
  BookOpen, 
  Play, 
  Code, 
  ExternalLink, 
  Download,
  Zap,
  Settings,
  FileCode
} from "lucide-react";
import Link from "next/link";

export default function TutorialsPage() {
  const tutorials = {
    cursor: [
      {
        title: "Getting Started with Cursor AI",
        description: "Learn the basics of setting up and using Cursor IDE with AI assistance",
        duration: "15 min",
        level: "Beginner",
        topics: ["Installation", "First Steps", "Basic Commands"]
      },
      {
        title: "Advanced Prompt Engineering in Cursor",
        description: "Master advanced techniques for writing effective AI prompts",
        duration: "30 min",
        level: "Advanced",
        topics: ["Prompt Patterns", "Context Management", "Code Generation"]
      },
      {
        title: "Custom Rules and Workflows",
        description: "Create custom AI rules and automate your coding workflows",
        duration: "25 min",
        level: "Intermediate",
        topics: ["Rule Creation", "Automation", "Best Practices"]
      }
    ],
    augment: [
      {
        title: "Augment Code Setup Guide",
        description: "Complete setup guide for Augment Code IDE integration",
        duration: "20 min",
        level: "Beginner",
        topics: ["Installation", "Configuration", "First Project"]
      },
      {
        title: "AI-Powered Code Review",
        description: "Use Augment Code for intelligent code review and suggestions",
        duration: "25 min",
        level: "Intermediate",
        topics: ["Code Analysis", "Review Workflows", "Quality Checks"]
      }
    ],
    windsurf: [
      {
        title: "Windsurf IDE Introduction",
        description: "Get started with Windsurf IDE and its AI capabilities",
        duration: "18 min",
        level: "Beginner",
        topics: ["Interface Overview", "AI Features", "Project Setup"]
      },
      {
        title: "Collaborative AI Development",
        description: "Learn to work collaboratively with AI in Windsurf",
        duration: "35 min",
        level: "Advanced",
        topics: ["Team Workflows", "Shared Contexts", "AI Collaboration"]
      }
    ]
  };

  const quickStarts = [
    {
      icon: Download,
      title: "Install OnlyRules CLI",
      description: "Quick setup to start using shared prompt rules",
      command: "npm install -g onlyrules"
    },
    {
      icon: Code,
      title: "Import Community Rules",
      description: "Browse and import rules from the community",
      command: "onlyrules import <rule-id>"
    },
    {
      icon: Settings,
      title: "Configure Your IDE",
      description: "Set up your preferred IDE with AI rules",
      command: "onlyrules configure"
    }
  ];

  const bestPractices = [
    "Be specific and clear in your prompt instructions",
    "Use context-aware prompts that understand your codebase",
    "Create reusable rule templates for common tasks",
    "Test your rules with different scenarios before sharing",
    "Document your rules with clear descriptions and examples",
    "Keep rules focused on single, specific tasks",
    "Use proper tagging to make rules discoverable"
  ];

  return (
    <div className="container py-8 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-2 mb-4">
          <BookOpen className="h-8 w-8 text-primary" />
          <h1 className="text-4xl font-bold">Tutorials & Guides</h1>
        </div>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Learn how to maximize your productivity with AI-powered IDEs and prompt engineering
        </p>
      </div>

      {/* Quick Start */}
      <section className="space-y-6">
        <h2 className="text-2xl font-bold">Quick Start</h2>
        <div className="grid md:grid-cols-3 gap-6">
          {quickStarts.map((item, index) => (
            <Card key={index}>
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                    <item.icon className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{item.title}</CardTitle>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">{item.description}</p>
                <div className="bg-muted p-3 rounded-lg">
                  <code className="text-sm font-mono">{item.command}</code>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* IDE-Specific Tutorials */}
      <section className="space-y-6">
        <h2 className="text-2xl font-bold">IDE-Specific Tutorials</h2>
        
        <Tabs defaultValue="cursor" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="cursor">Cursor</TabsTrigger>
            <TabsTrigger value="augment">Augment Code</TabsTrigger>
            <TabsTrigger value="windsurf">Windsurf</TabsTrigger>
          </TabsList>
          
          {Object.entries(tutorials).map(([ide, tutorialList]) => (
            <TabsContent key={ide} value={ide} className="space-y-4">
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {tutorialList.map((tutorial, index) => (
                  <Card key={index} className="group hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="space-y-2">
                          <CardTitle className="text-lg group-hover:text-primary transition-colors">
                            {tutorial.title}
                          </CardTitle>
                          <CardDescription>{tutorial.description}</CardDescription>
                        </div>
                        <Badge variant={
                          tutorial.level === "Beginner" ? "soft" :
                          tutorial.level === "Intermediate" ? "solid" : "solid"
                        }>
                          {tutorial.level}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Play className="h-4 w-4" />
                          {tutorial.duration}
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <span className="text-sm font-medium">Topics covered:</span>
                        <div className="flex flex-wrap gap-1">
                          {tutorial.topics.map((topic) => (
                            <Badge key={topic} variant="outline" className="text-xs">
                              {topic}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      
                      <Button className="w-full" variant="outline">
                        <Play className="mr-2 h-4 w-4" />
                        Start Tutorial
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </section>

      {/* Best Practices */}
      <section className="space-y-6">
        <h2 className="text-2xl font-bold">Best Practices</h2>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-primary" />
              Prompt Engineering Best Practices
            </CardTitle>
            <CardDescription>
              Follow these guidelines to create effective AI prompt rules
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-3">
              {bestPractices.map((practice, index) => (
                <li key={index} className="flex items-start gap-3">
                  <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center mt-0.5">
                    <span className="text-xs font-bold text-primary">{index + 1}</span>
                  </div>
                  <span className="text-sm">{practice}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      </section>

      {/* Resources */}
      <section className="space-y-6">
        <h2 className="text-2xl font-bold">Additional Resources</h2>
        <div className="grid md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileCode className="h-5 w-5" />
                Documentation
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Comprehensive documentation for all supported IDEs and features.
              </p>
              <Button variant="outline" className="w-full">
                <ExternalLink className="mr-2 h-4 w-4" />
                View Documentation
              </Button>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Code className="h-5 w-5" />
                Example Rules
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Browse a curated collection of high-quality example rules.
              </p>
              <Button variant="outline" className="w-full" asChild>
                <Link href="/templates">
                  View Examples
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  );
}