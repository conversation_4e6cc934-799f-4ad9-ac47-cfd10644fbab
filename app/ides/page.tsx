import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { <PERSON>, <PERSON>Head<PERSON>, Card<PERSON><PERSON>le, CardDescription } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>R<PERSON>, Code2, <PERSON>rk<PERSON> } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Supported AI Code IDEs',
  description: 'Explore OnlyRules integration guides for all supported AI-powered code IDEs. Learn how to enhance your coding workflow with AI assistance.',
  alternates: {
    canonical: '/ides',
  },
};

interface IDE {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  features: string[];
}

const supportedIDEs: IDE[] = [
  {
    id: 'cursor',
    name: 'Cursor',
    description: 'The AI-first code editor built for pair programming with AI',
    icon: '🔷',
    color: 'bg-blue-500',
    features: ['AI-powered code completion', 'Natural language to code', 'Intelligent refactoring'],
  },
  {
    id: 'augment',
    name: 'Augment Code',
    description: 'Enhance your coding experience with advanced AI assistance',
    icon: '🟣',
    color: 'bg-purple-500',
    features: ['Smart code suggestions', 'Context-aware completions', 'Code optimization'],
  },
  {
    id: 'windsurf',
    name: 'Windsurf',
    description: 'Ride the wave of AI-powered development with Windsurf IDE',
    icon: '🌊',
    color: 'bg-cyan-500',
    features: ['Flow-based coding', 'AI pair programming', 'Real-time collaboration'],
  },
  {
    id: 'claude',
    name: 'Claude Dev',
    description: 'Anthropic\'s Claude integrated into your development environment',
    icon: '🤖',
    color: 'bg-orange-500',
    features: ['Advanced reasoning', 'Long context windows', 'Ethical AI coding'],
  },
  {
    id: 'github-copilot',
    name: 'GitHub Copilot',
    description: 'Your AI pair programmer powered by OpenAI Codex',
    icon: '🐙',
    color: 'bg-gray-700',
    features: ['Code suggestions', 'Multi-language support', 'Test generation'],
  },
  {
    id: 'gemini',
    name: 'Gemini Code Assist',
    description: 'Google\'s multimodal AI for enhanced code development',
    icon: '✨',
    color: 'bg-indigo-500',
    features: ['Multimodal understanding', 'Code explanation', 'Bug detection'],
  },
  {
    id: 'openai-codex',
    name: 'OpenAI Codex',
    description: 'The AI system that powers GitHub Copilot and more',
    icon: '🧠',
    color: 'bg-green-600',
    features: ['Natural language to code', 'Code translation', 'Documentation generation'],
  },
  {
    id: 'cline',
    name: 'Cline',
    description: 'Autonomous AI assistant for complex coding tasks',
    icon: '🎯',
    color: 'bg-red-500',
    features: ['Autonomous coding', 'Multi-file editing', 'Project understanding'],
  },
  {
    id: 'tencent-codebuddy',
    name: 'Tencent CodeBuddy',
    description: 'Enterprise-grade AI coding assistant from Tencent Cloud',
    icon: '☁️',
    color: 'bg-blue-600',
    features: ['Enterprise features', 'Security focused', 'Cloud integration'],
  },
];

export default function IDEsPage() {
  return (
    <div className="container mx-auto px-4 py-12">
      {/* Hero Section */}
      <div className="text-center mb-12">
        <div className="flex items-center justify-center gap-2 mb-4">
          <Code2 className="h-8 w-8 text-primary" />
          <h1 className="text-4xl font-bold">Supported AI Code IDEs</h1>
        </div>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          OnlyRules seamlessly integrates with the most popular AI-powered code editors. 
          Choose your IDE and learn how to supercharge your development workflow with custom prompt rules.
        </p>
      </div>

      {/* Features Overview */}
      <div className="bg-muted/50 rounded-lg p-8 mb-12">
        <h2 className="text-2xl font-semibold mb-4 flex items-center gap-2">
          <Sparkles className="h-6 w-6 text-primary" />
          Why Use OnlyRules with Your IDE?
        </h2>
        <div className="grid md:grid-cols-3 gap-6">
          <div>
            <h3 className="font-semibold mb-2">🎯 Tailored Prompts</h3>
            <p className="text-sm text-muted-foreground">
              Create IDE-specific prompt rules that leverage each platform&apos;s unique features
            </p>
          </div>
          <div>
            <h3 className="font-semibold mb-2">🔄 Easy Integration</h3>
            <p className="text-sm text-muted-foreground">
              Simple setup process with step-by-step guides for each supported IDE
            </p>
          </div>
          <div>
            <h3 className="font-semibold mb-2">🚀 Boost Productivity</h3>
            <p className="text-sm text-muted-foreground">
              Enhance your AI coding assistant with community-driven best practices
            </p>
          </div>
        </div>
      </div>

      {/* IDE Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {supportedIDEs.map((ide) => (
          <Card key={ide.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between mb-4">
                <div className={`p-3 rounded-lg ${ide.color} bg-opacity-10 inline-block`}>
                  <span className="text-3xl">{ide.icon}</span>
                </div>
                <div className={`h-2 w-2 rounded-full ${ide.color}`} />
              </div>
              <CardTitle className="text-xl mb-2">{ide.name}</CardTitle>
              <CardDescription className="mb-4">{ide.description}</CardDescription>
              
              <div className="space-y-2 mb-4">
                {ide.features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    <div className={`h-1.5 w-1.5 rounded-full ${ide.color}`} />
                    <span className="text-muted-foreground">{feature}</span>
                  </div>
                ))}
              </div>

              <Link href={`/ides/${ide.id}`}>
                <Button className="w-full group">
                  View Integration Guide
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
            </CardHeader>
          </Card>
        ))}
      </div>

      {/* CTA Section */}
      <div className="mt-16 text-center bg-primary/5 rounded-lg p-8">
        <h2 className="text-2xl font-semibold mb-4">Don&apos;t see your IDE?</h2>
        <p className="text-muted-foreground mb-6">
          We&apos;re constantly adding support for new AI-powered development tools. 
          Let us know which IDE you&apos;d like to see next!
        </p>
        <Button variant="outline" size="3">
          Request IDE Support
        </Button>
      </div>
    </div>
  );
}