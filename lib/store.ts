"use client";

import { atom } from 'jotai';
import { atomWithStorage } from 'jotai/utils';

export type IDEType = 'CURSOR' | 'AUGMENT' | 'WINDSURF' | 'CLAUDE' | 'GITHUB_COPILOT' | 'GEMINI' | 'OPENAI_CODEX' | 'CLINE' | 'JUNIE' | 'TRAE' | 'LINGMA' | 'KIRO' | 'TENCENT_CODEBUDDY' | 'GENERAL';
export type VisibilityType = 'PRIVATE' | 'PUBLIC';

export interface Rule {
  id: string;
  title: string;
  description?: string;
  content: string;
  ideType: IDEType;
  visibility: VisibilityType;
  shareToken?: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  tags: { tag: { id: string; name: string; color: string } }[];
}

export interface Tag {
  id: string;
  name: string;
  color: string;
}

export interface RulePayload {
  id?: string;
  title: string;
  description?: string;
  content: string;
  ideType: IDEType;
  visibility: VisibilityType;
  shareToken?: string;
  createdAt?: string;
  updatedAt?: string;
  userId?: string;
  tags: string[];
}

export const rulesAtom = atom<Rule[]>([]);
export const tagsAtom = atom<Tag[]>([]);
export const selectedRuleAtom = atom<Rule | null>(null);
export const searchQueryAtom = atom<string>('');
export const selectedTagsAtom = atom<string[]>([]);
export const selectedIDEAtom = atom<string>('ALL');
export const themeAtom = atomWithStorage('theme', 'system');